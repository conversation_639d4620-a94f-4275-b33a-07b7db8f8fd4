import type { UserConfig, ConfigEnv } from 'vite'
import pkg from './package.json'
import dayjs from 'dayjs'
import { loadEnv } from 'vite'
import { resolve } from 'path'
import { generateModifyVars } from './build/generate/generateModifyVars'
import { createProxy } from './build/vite/proxy'
import { wrapperEnv } from './build/utils'
import { createVitePlugins } from './build/vite/plugin'
import { OUTPUT_DIR } from './build/constant'

function pathResolve(dir: string) {
    return resolve(process.cwd(), '.', dir)
}

const { dependencies, devDependencies, name, version } = pkg
const __APP_INFO__ = {
    pkg: { dependencies, devDependencies, name, version },
    lastBuildTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
}

export default ({ command, mode }: ConfigEnv): UserConfig => {
    const root = process.cwd()

    const env = loadEnv(mode, root)

    // The boolean type read by loadEnv is a string. This function can be converted to boolean type
    const viteEnv = wrapperEnv(env)

    const { VITE_PORT, VITE_PUBLIC_PATH, VITE_PROXY } = viteEnv

    const isBuild = command === 'build'

    const serverOptions: Recordable = {}

    // ----- [begin] 【JEECG作为乾坤子应用】 -----
    const { VITE_GLOB_QIANKUN_MICRO_APP_NAME, VITE_GLOB_QIANKUN_MICRO_APP_ENTRY } = viteEnv
    const isQiankunMicro = VITE_GLOB_QIANKUN_MICRO_APP_NAME != null && VITE_GLOB_QIANKUN_MICRO_APP_NAME !== ''
    if (isQiankunMicro && !isBuild) {
        serverOptions.cors = true
        serverOptions.origin = VITE_GLOB_QIANKUN_MICRO_APP_ENTRY!.split('/').slice(0, 3).join('/')
    }
    // ----- [end] 【JEECG作为乾坤子应用】 -----

    return {
        // base: isQiankunMicro ? VITE_GLOB_QIANKUN_MICRO_APP_ENTRY : VITE_PUBLIC_PATH,
        base: isQiankunMicro ? '/gac_iop_childapp/' : VITE_PUBLIC_PATH,
        root,
        resolve: {
            alias: [
                {
                    find: 'vue-i18n',
                    replacement: 'vue-i18n/dist/vue-i18n.cjs.js'
                },
                // /@/xxxx => src/xxxx
                {
                    find: /\/@\//,
                    replacement: pathResolve('src') + '/'
                },
                // /#/xxxx => types/xxxx
                {
                    find: /\/#\//,
                    replacement: pathResolve('types') + '/'
                },
                {
                    find: /@\//,
                    replacement: pathResolve('src') + '/'
                },
                // /#/xxxx => types/xxxx
                {
                    find: /#\//,
                    replacement: pathResolve('types') + '/'
                }
            ]
        },
        server: {
            // Listening on all local IPs
            host: true,
            // @ts-ignore
            https: false,
            port: VITE_PORT,
            // Load proxy configuration from .env
            proxy: createProxy(VITE_PROXY),
            open: true,
            cors: true,
            // 合并 server 配置
            ...serverOptions
        },
        build: {
            minify: 'esbuild',
            target: 'es2020', // 升级到es2020，减少polyfill
            cssTarget: 'chrome80',
            outDir: OUTPUT_DIR,
            rollupOptions: {
                // 关闭除屑优化，防止删除重要代码，导致打包后功能出现异常
                treeshake: false,
                output: {
                    chunkFileNames: 'js/[name]-[hash].js', // 引入文件名的名称
                    entryFileNames: 'js/[name]-[hash].js', // 包的入口文件名称
                    // manualChunks配置 - 优化分包策略 (5个→12个分包)
                    manualChunks: {
                        // Vue生态核心 (小包，高频使用)
                        'vue-vendor': ['vue', 'vue-router', 'pinia'],

                        // UI框架主体 (大包，但必需)
                        'antd-vue-vendor': ['ant-design-vue', '@ant-design/colors'],

                        // 图标库单独分包 (中等大小，按需加载)
                        'antd-icons-vendor': ['@ant-design/icons-vue'],

                        // 表格组件 (大包，特定页面使用)
                        'vxe-table-vendor': ['vxe-table', 'vxe-table-plugin-antd', 'xe-utils'],

                        // 图表库 (大包，特定页面使用)
                        'chart-vendor': ['echarts'],

                        // 富文本编辑器 (超大包，特定功能使用) - 已移除
                        // 'editor-vendor': ['tinymce', '@tinymce/tinymce-vue'], // 已移除

                        // 工具库 (中等大小，高频使用) - 注意：不包含lodash-es，让Vite自动处理
                        'utils-vendor': ['dayjs', 'axios', 'moment'],

                        // 移动端UI (大包，移动端页面使用)
                        'vant-vendor': ['vant'],

                        // 地区数据 (中等大小，特定功能使用)
                        'area-data-vendor': ['@vant/area-data'],

                        // 表情包 (中等大小，特定功能使用)
                        'emoji-vendor': ['emoji-mart-vue-fast']

                        // 特殊API (小包，框架级别) - 已移除dingtalk-jsapi
                        // 'api-vendor': ['dingtalk-jsapi'], // 已移除
                    }
                }
            },
            // 关闭brotliSize显示可以稍微减少打包时间
            reportCompressedSize: false,
            // 提高超大静态资源警告大小
            chunkSizeWarningLimit: 2000
        },
        esbuild: {
            // 优化esbuild配置
            target: 'es2020',
            // 生产环境清除console和debugger
            drop: isBuild ? ['console', 'debugger'] : []
        },
        define: {
            // setting vue-i18-next
            // Suppress warning
            __INTLIFY_PROD_DEVTOOLS__: false,
            __APP_INFO__: JSON.stringify(__APP_INFO__)
        },
        css: {
            preprocessorOptions: {
                less: {
                    modifyVars: generateModifyVars(),
                    javascriptEnabled: true
                }
            }
        },

        // The vite plugin used by the project. The quantity is large, so it is separately extracted and managed
        // 预加载构建配置（首屏性能)
        plugins: createVitePlugins(viteEnv, isBuild, isQiankunMicro),
        optimizeDeps: {
            esbuildOptions: {
                target: 'es2020' // 与构建target保持一致
            },
            exclude: [
                //升级vite4后，需要排除online依赖
                '@jeecg/online'
            ]
        }
    }
}
