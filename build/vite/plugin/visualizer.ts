/**
 * Package file volume analysis - 使用简化的分析方案
 */
import { isReportMode } from '../../utils';
import type { Plugin } from 'vite';

export function configVisualizerConfig(): Plugin | Plugin[] {
  if (isReportMode()) {
    // 使用Vite内置的构建分析功能
    return {
      name: 'bundle-analyzer',
      generateBundle(options, bundle) {
        // 分析bundle信息
        const bundleInfo: Record<string, any> = {};

        for (const [fileName, chunk] of Object.entries(bundle)) {
          if (chunk.type === 'chunk') {
            bundleInfo[fileName] = {
              size: chunk.code.length,
              modules: Object.keys(chunk.modules || {}),
              imports: chunk.imports,
              exports: chunk.exports,
              isDynamicEntry: chunk.isDynamicEntry,
              isEntry: chunk.isEntry
            };
          } else if (chunk.type === 'asset') {
            bundleInfo[fileName] = {
              size: chunk.source.length,
              type: 'asset'
            };
          }
        }

        // 输出分析结果到控制台
        console.log('\n📊 Bundle Analysis:');
        console.log('==================');

        const chunks = Object.entries(bundleInfo)
          .filter(([_, info]) => info.type !== 'asset')
          .sort(([_, a], [__, b]) => (b.size || 0) - (a.size || 0));

        chunks.forEach(([fileName, info]) => {
          const sizeKB = ((info.size || 0) / 1024).toFixed(2);
          console.log(`📦 ${fileName}: ${sizeKB}KB`);
          if (info.modules && info.modules.length > 0) {
            console.log(`   📁 Modules: ${info.modules.length}`);
            // 显示最大的几个模块
            const topModules = info.modules.slice(0, 3);
            topModules.forEach((mod: string) => {
              console.log(`      - ${mod}`);
            });
            if (info.modules.length > 3) {
              console.log(`      ... and ${info.modules.length - 3} more`);
            }
          }
        });

        console.log('==================\n');

        // 保存详细信息到文件
        this.emitFile({
          type: 'asset',
          fileName: 'bundle-analysis.json',
          source: JSON.stringify(bundleInfo, null, 2)
        });
      }
    } as Plugin;
  }
  return [];
}
