<template>
  <div class="j-markdown-editor-placeholder">
    <a-textarea
      :value="value"
      :disabled="disabled"
      :rows="10"
      placeholder="Markdown编辑器已移除，请使用普通文本输入"
      @change="onChange"
    />
    <div class="editor-notice">
      <a-alert
        message="Markdown编辑器已移除"
        description="Markdown编辑器组件已被移除以减少包体积，当前使用普通文本域替代"
        type="info"
        show-icon
        closable
      />
    </div>
  </div>
</template>

<script lang="ts">
  import { defineComponent, nextTick } from 'vue';
  // import { MarkDown } from '/@/components/Markdown'; // 已移除Markdown组件
  import { propTypes } from '/@/utils/propTypes';
  import { Form, Textarea, Alert } from 'ant-design-vue';
  export default defineComponent({
    name: 'JMarkdownEditor',
    // 不将 attrs 的属性绑定到 html 标签上
    inheritAttrs: false,
    components: {
      'a-textarea': Textarea,
      'a-alert': Alert
    },
    props: {
      value: propTypes.string.def(''),
      disabled: propTypes.bool.def(false),
    },
    emits: ['change', 'update:value'],
    setup(props, { emit }) {
      const formItemContext = Form.useInjectFormItemContext();

      // value change 事件
      function onChange(e: Event) {
        const value = (e.target as HTMLTextAreaElement)?.value || '';
        emit('change', value);
        emit('update:value', value);
        // update-begin--author:liaozhiyang---date:20240429---for：【QQYUN-9110】组件有值校验没消失
        nextTick(() => {
          formItemContext?.onFieldChange();
        });
        // update-end--author:liaozhiyang---date:20240429---for：【QQYUN-9110】组件有值校验没消失
      }

      return {
        onChange,
      };
    },
  });
</script>

<style lang="less" scoped></style>
