import type { AppRouteRecordRaw, AppRouteModule } from '/@/router/types'

import { PAGE_NOT_FOUND_ROUTE, REDIRECT_ROUTE } from '/@/router/routes/basic'

import { PageEnum } from '/@/enums/pageEnum'
import { t } from '/@/hooks/web/useI18n'
// import dashboardRoute from './modules/dashboard' // 已删除dashboard模块

const modules = import.meta.glob('./modules/**/*.ts', { eager: true })

const routeModuleList: AppRouteModule[] = []

// 加入到路由集合中
Object.keys(modules).forEach(key => {
    const mod = (modules as Recordable)[key].default || {}
    const modList = Array.isArray(mod) ? [...mod] : [mod]
    routeModuleList.push(...modList)
})

export const asyncRoutes = [PAGE_NOT_FOUND_ROUTE, ...routeModuleList]

export const RootRoute: AppRouteRecordRaw = {
    path: '/',
    name: 'Root',
    redirect: PageEnum.BASE_HOME,
    meta: {
        title: 'Root'
    }
}

export const LoginRoute: AppRouteRecordRaw = {
    path: '/login',
    name: 'Login',
    component: () => import('/@/views/system/loginmini/MiniLogin.vue'),
    //component: () => import('/@/views/mobileH5/index/index.vue'),
    //component: () => import('/@/views/mobileH5/clueList/clueList.vue'),
    //component: () => import('/@/views/mobileH5/allocation/allocation.vue'),
    //component: () => import('/@/views/mobileH5/clueDetail/clueDetail.vue'),
    meta: {
        ignoreAuth: false,
        title: t('routes.basic.login')
    }
}

/**
 * 【通过token直接静默登录】流程办理登录页面 中转跳转
 */
export const TokenLoginRoute: AppRouteRecordRaw = {
    path: '/tokenLogin',
    name: 'TokenLoginRoute',
    component: () => import('/@/views/sys/login/TokenLoginPage.vue'),
    meta: {
        title: '带token登录页面',
        ignoreAuth: true
    }
}

// export const clueTodoH5: AppRouteRecordRaw = {
//     path: '/gac_iop_childapp/clue-todo-h5',
//     name: 'clueTodoH5',
//     component: () => import('/@/views/mobileH5/clueTodoH5/index.vue'),
//     meta: {
//         title: '移动h5',
//         ignoreAuth: false
//     }
// }
// // 工单待办
// export const workOrderTodoH5: AppRouteRecordRaw = {
//     path: '/gac_iop_childapp/work-order-todo-h5',
//     name: 'workOrderTodoH5',
//     component: () => import('/@/views/mobileH5/workOrderTodoH5/index.vue'),
//     meta: {
//         title: '移动h5',
//         ignoreAuth: false
//     }
// }
// export const clueList: AppRouteRecordRaw = {
//     path: '/gac_iop_childapp/clue-list-h5',
//     name: 'clueList',
//     component: () => import('/@/views/mobileH5/clueList/clueList.vue'),
//     meta: {
//         title: '线索列表',
//         ignoreAuth: false
//     }
// }
// export const allocation: AppRouteRecordRaw = {
//     path: '/gac_iop_childapp/work-order-allocation-h5',
//     name: 'allocation',
//     component: () => import('/@/views/mobileH5/allocation/allocation.vue'),
//     meta: {
//         title: '分配',
//         ignoreAuth: false
//     }
// }
// export const clueAllocation: AppRouteRecordRaw = {
//     path: '/gac_iop_childapp/clue-allocation-h5',
//     name: 'allocation',
//     component: () => import('/@/views/mobileH5/allocation/allocation.vue'),
//     meta: {
//         title: '分配',
//         ignoreAuth: false
//     }
// }
// export const clueDetail: AppRouteRecordRaw = {
//     path: '/gac_iop_childapp/clue-detail-h5',
//     name: 'clueDetail',
//     component: () => import('/@/views/mobileH5/clueDetail/clueDetail.vue'),
//     meta: {
//         title: '线索详情',
//         ignoreAuth: false
//     }
// }

// export const workOrderList: AppRouteRecordRaw = {
//     path: '/gac_iop_childapp/work-order-list-h5',
//     name: 'workOrderList',
//     component: () => import('/@/views/mobileH5/workOrderList/workOrderList.vue'),
//     meta: {
//         title: '工单列表',
//         ignoreAuth: false
//     }
// }

// export const reissue: AppRouteRecordRaw = {
//     path: '/gac_iop_childapp/work-order-reissue-h5',
//     name: 'reissue',
//     component: () => import('/@/views/mobileH5/reissue/reissue.vue'),
//     meta: {
//         title: '工单重新下发',
//         ignoreAuth: false
//     }
// }

// export const workOrderProcessing: AppRouteRecordRaw = {
//     path: '/gac_iop_childapp/work-order-processing-h5',
//     name: 'workOrderProcessing',
//     component: () => import('/@/views/mobileH5/workOrderProcessing/workOrderProcessing.vue'),
//     meta: {
//         title: '工单处理',
//         ignoreAuth: false
//     }
// }
// export const workOrderDetail: AppRouteRecordRaw = {
//     path: '/gac_iop_childapp/work-order-detail-h5',
//     name: 'workOrderDetail',
//     component: () => import('/@/views/mobileH5/workOrderDetail/workOrderDetail.vue'),
//     meta: {
//         title: '工单详情',
//         ignoreAuth: false
//     }
// }

// 接口未联调前，先手动把路由放进去
export const basicRoutes = [
    // dashboardRoute, // 已删除dashboard模块
    LoginRoute,
    RootRoute,
    REDIRECT_ROUTE,
    PAGE_NOT_FOUND_ROUTE,
    TokenLoginRoute,
    // clueTodoH5,
    // workOrderTodoH5,
    // clueList,
    // allocation,
    // clueAllocation,
    // clueDetail,
    // workOrderList,
    // reissue,
    // workOrderProcessing,
    // workOrderDetail
]
