import { useUserStoreWithOut } from '/@/store/modules/user'
import { handleDateChange } from '/@/utils/timezoneUtil'

const LANG = localStorage.getItem('lang')
const IS_ZH_CN = !['en', 'es', 'ru', 'ar'].includes(LANG)

export default function useMinxin() {
    const userStore = useUserStoreWithOut()
    /**
     * 创建字典下拉选项
     * @param code 数据字典的 code 码
     * @param vals 需要过滤数据字典项的值
     * @param reversed
     * @returns
     */
    const createDictList = (code: String, vals?: Array<any>, reversed?: any) => {
        const dict = userStore.getDict
        vals = Array.isArray(vals) ? vals : [vals]
        let res = []
        if (Array.isArray(dict[code])) {
            res = dict[code].map(x => ({
                label: x.codeDesc,
                value: x.codeId
            }))
            res = res.filter(x => {
                if (!reversed) return !vals.includes(x.value)
                return vals.includes(x.value)
            })
        }
        return res
    }
    /**
     * 创建大区下拉选项
     * @returns
     */
    const createRegionList = () => {
        const region = userStore.getRegion
        let res = []
        if (region) {
            for (let i in region) {
                const { regionName, regionCode } = region[i]
                res.push({
                    label: regionName,
                    value: regionCode
                })
            }
        }
        return res
    }
    /**
     * 创建国家下拉选项
     * @returns
     */
    const createCountryList = () => {
        const countryList = userStore.getCountry
        let res = []
        if (countryList) {
            for (let i in countryList) {
                const { countryName, countryCode } = countryList[i]
                res.push({
                    label: countryName,
                    value: countryCode
                })
            }
        }
        return res
    }
    /**
     * 将字典值转成文本
     * @param value
     * @param code
     * @returns
     */
    const getLabelFromDict = (value: any, code: String) => {
        const dict = userStore.getDict
        let res = []
        if (Array.isArray(dict[code])) {
            res = dict[code].map(x => ({
                label: x.codeDesc,
                value: x.codeId
            }))
        }
        const label = res.find(n => n.value == value)?.label ?? value
        return label
    }

    const downloadBlob = ({ data, headers }) => {
        let tip
        if(headers.responsemsg){
            tip = decodeURI(headers.responsemsg)
            return tip
        }else{
            let fileUrl = window.URL.createObjectURL(new Blob([data]))
            let a = document.createElement('a')
            a.style.display ='none'
            a.href = fileUrl
            a.setAttribute('download',window.decodeURIComponent(headers.filename))
            document.body.appendChild(a)
            a.click()
            window.URL.revokeObjectURL(a.href)
            document.body.removeChild(a)
        }
    }
    // 使用导入的handleDateChange函数

    return {
        createDictList,
        createRegionList,
        createCountryList,
        getLabelFromDict,
        downloadBlob,
        handleDateChange
    }
}
