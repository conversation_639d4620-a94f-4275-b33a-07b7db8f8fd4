/**
 * 时区处理工具函数 - 使用dayjs替代moment-timezone
 */
import dayjs from 'dayjs';

/**
 * 获取本地时区名称
 * @returns 本地时区名称，如 "Asia/Shanghai"
 */
export function getLocalTimezone(): string {
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
}

/**
 * 获取本地时区偏移量字符串
 * @returns 格式化的时区偏移量，如 "UTC+08:00"
 */
export function getLocalTimeZoneOffset(): string {
  const localZone = getLocalTimezone();
  const now = dayjs().tz(localZone);
  const offset = now.utcOffset();

  // 计算符号、小时、分钟
  const sign = offset >= 0 ? '+' : '-';
  const absOffset = Math.abs(offset);
  const hours = Math.floor(absOffset / 60);
  const minutes = absOffset % 60;

  // 格式化为两位数
  const formattedHours = String(hours).padStart(2, '0');
  const formattedMinutes = String(minutes).padStart(2, '0');

  return `UTC${sign}${formattedHours}:${formattedMinutes}`;
}

/**
 * 处理日期变化，转换为本地时区格式
 * @param date 日期或日期数组
 * @returns 格式化后的日期字符串或字符串数组
 */
export function handleDateChange(date: any): any {
  const localTimezone = getLocalTimezone();
  let formattedDates = date;
  
  if (date) {
    if (date instanceof Array) {
      formattedDates = date.map(item => 
        dayjs(item).tz(localTimezone).format('YYYY-MM-DDTHH:mm:ssZ')
      );
    } else {
      formattedDates = dayjs(date).tz(localTimezone).format('YYYY-MM-DDTHH:mm:ssZ');
    }
  }
  
  return formattedDates;
}

/**
 * 将日期转换为指定时区
 * @param date 日期
 * @param timezone 目标时区
 * @param format 输出格式
 * @returns 格式化后的日期字符串
 */
export function convertToTimezone(
  date: any, 
  timezone: string, 
  format: string = 'YYYY-MM-DDTHH:mm:ssZ'
): string {
  return dayjs(date).tz(timezone).format(format);
}

/**
 * 获取当前时间的UTC时间戳
 * @returns UTC时间戳
 */
export function getUtcTimestamp(): number {
  return dayjs().utc().valueOf();
}
