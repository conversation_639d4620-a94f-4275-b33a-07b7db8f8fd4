// axios配置  可自行根据项目进行更改，只需更改该文件即可，其他文件可以不动
// The axios configuration can be changed according to the project, just change the file, other files can be left unchanged

import type { AxiosResponse } from 'axios'
import type { RequestOptions } from '/#/axios'
import type { AxiosTransform, CreateAxiosOptions } from './axiosTransform'
import { VAxios } from './Axios'
import { checkStatus } from './checkStatus'
// import { router } from '/@/router';
import { useGlobSetting } from '/@/hooks/setting'
import { useMessage } from '/@/hooks/web/useMessage'
import { RequestEnum, ResultEnum, ContentTypeEnum, ConfigEnum } from '/@/enums/httpEnum'
import { isString } from '/@/utils/is'
import { setObjToUrlParams, deepMerge } from '/@/utils'
// import signMd5Utils from '/@/utils/encryption/signMd5Utils';
import { useLocaleStoreWithOut } from '/@/store/modules/locale'
import { useErrorLogStoreWithOut } from '/@/store/modules/errorLog'
import { useI18n } from '/@/hooks/web/useI18n'
import { joinTimestamp, formatRequestDate } from './helper'
import { useUserStoreWithOut } from '/@/store/modules/user'
import { getLocalTimeZoneOffset } from '/@/utils/timezoneUtil'

// import { cloneDeep } from 'lodash-es';
const globSetting = useGlobSetting()
const urlPrefix = globSetting.urlPrefix
const { createMessage, notification, createErrorModal } = useMessage()

// getLocalTimeZoneOffset 函数已移至 timezoneUtil.ts

/**
 * @description: 数据处理，方便区分多种处理方式
 */
const transform: AxiosTransform = {
    /**
     * @description: 处理请求数据。如果数据不是预期格式，可直接抛出错误
     */
    transformRequestHook: (res: any, options: RequestOptions) => {
        const { t } = useI18n()
        const { isTransformResponse, isReturnNativeResponse } = options
        // 是否返回原生响应头 比如：需要获取响应头时使用该属性
        if (isReturnNativeResponse) {
            return res
        }
        if (!isTransformResponse) {
            return res.data
        }
        // 错误的时候返回
        if (!res.data) {
            // return '[HTTP] Request has no return value';
            throw new Error(t('sys.api.apiRequestFailed'))
        }
        // 判断是否为导出/下载附件
        if (res?.config?.responseType === 'blob' || res?.config?.responseType === 'arraybuffer') return res

        const { resultCode, data, errMsg, content, code } = res.data
        const hasSuccess = res.data && Reflect.has(res.data, 'code') && (code === ResultEnum.SUCCESS || code === 200)
        if (resultCode === 200 || hasSuccess) {
            if (resultCode === 200) return data || res.data
            if (hasSuccess) return content || res.data
        }

        let timeoutMsg = ''
        switch (resultCode) {
            case ResultEnum.TIMEOUT:
                timeoutMsg = t('sys.api.timeoutMessage')
                const userStore = useUserStoreWithOut()
                userStore.setToken(undefined)
                userStore.logout(true)
                break
            default:
                if (errMsg) {
                    timeoutMsg = errMsg
                }
        }

        // errorMessageMode=‘modal’的时候会显示modal错误弹窗，而不是消息提示，用于一些比较重要的错误
        // errorMessageMode='none' 一般是调用时明确表示不希望自动弹出错误提示
        if (options.errorMessageMode === 'modal') {
            createErrorModal({ title: t('sys.api.errorTip'), content: timeoutMsg })
        } else if (options.errorMessageMode === 'message') {
            createMessage.error(timeoutMsg)
        }

        throw new Error(timeoutMsg || t('sys.api.apiRequestFailed'))
    },

    // 请求之前处理config
    beforeRequestHook: (config, options) => {
        const { apiUrl, joinPrefix, joinParamsToUrl, formatDate, joinTime = true, urlPrefix } = options

        //update-begin---author:scott ---date:2024-02-20  for：以http开头的请求url，不拼加前缀--
        // http开头的请求url，不加前缀
        let isStartWithHttp = false
        const requestUrl = config.url
        if (requestUrl != null && (requestUrl.startsWith('http:') || requestUrl.startsWith('https:'))) {
            isStartWithHttp = true
        }
        if (!isStartWithHttp && joinPrefix) {
            config.url = `${urlPrefix}${config.url}`
        }

        if (!isStartWithHttp && apiUrl && isString(apiUrl)) {
            config.url = `${apiUrl}${config.url}`
        }
        //update-end---author:scott ---date::2024-02-20  for：以http开头的请求url，不拼加前缀--

        const params = config.params || {}
        const data = config.data || false
        formatDate && data && !isString(data) && formatRequestDate(data)
        if (config.method?.toUpperCase() === RequestEnum.GET) {
            if (!isString(params)) {
                // 给 get 请求加上时间戳参数，避免从缓存中拿数据。
                config.params = Object.assign(params || {}, joinTimestamp(joinTime, false))
            } else {
                // 兼容restful风格
                config.url = config.url + params + `${joinTimestamp(joinTime, true)}`
                config.params = undefined
            }
        } else {
            if (!isString(params)) {
                formatDate && formatRequestDate(params)
                if (Reflect.has(config, 'data') && config.data && Object.keys(config.data).length > 0) {
                    config.data = data
                    config.params = params
                } else {
                    // 非GET请求如果没有提供data，则将params视为data
                    config.data = params
                    config.params = undefined
                }
                if (joinParamsToUrl) {
                    config.url = setObjToUrlParams(config.url as string, Object.assign({}, config.params, config.data))
                }
            } else {
                // 兼容restful风格
                config.url = config.url + params
                config.params = undefined
            }
        }

        // update-begin--author:sunjianlei---date:220241019---for：【JEECG作为乾坤子应用】作为乾坤子应用启动时，拼接请求路径
        if (globSetting.isQiankunMicro) {
            if (config.url && config.url.startsWith('/')) {
                config.url = globSetting.qiankunMicroAppEntry + config.url
            }
        }
        // update-end--author:sunjianlei---date:220241019---for：【JEECG作为乾坤子应用】作为乾坤子应用启动时，拼接请求路径

        return config
    },

    /**
     * @description: 请求拦截器处理
     */
    requestInterceptors: (config: Recordable) => {
        const localeStore = useLocaleStoreWithOut()
        const userStore = useUserStoreWithOut()
        // 请求之前处理config
        const token = userStore.getToken
        // 获取时区
        const localTimezone = getLocalTimeZoneOffset()
        // config.headers[ConfigEnum.TOKEN] = `Bearer ${token}`;
        config.headers[ConfigEnum.TOKEN] = token
        config.headers[ConfigEnum.LOCAL_LABGUAGE] = localeStore.getLocale
        config.headers[ConfigEnum.X_USER_TIMEZONE] = localTimezone
        return config
    },

    /**
     * @description: 响应拦截器处理
     */
    responseInterceptors: async (res: AxiosResponse<any>) => {
        return new Promise(resolve => {
            // 判断是否为导出/下载附件
            if (res?.config?.responseType === 'blob' || res?.config?.responseType === 'arraybuffer') {
                if (res?.data.type === 'application/json') {
                    const file = new FileReader() // 读取文件
                    file.readAsText(res?.data, 'utf-8') // 读取文件，并设置编码格式为utf-8
                    let fileData
                    file.onload = function() {
                        // 在读取文件操作完成后触发
                        fileData = JSON.parse(file.result) // reader.result返回文件的内容，只在读取操作完成后有效
                        if (fileData.errMsg) {
                            notification.error({
                                message: fileData.errMsg,
                                placement: 'topRight',
                                duration: 3,
                            })
                            return
                        }
                        resolve(res)
                    }
                    return
                }
                resolve(res)
            } else {
                resolve(res)
            }
        })
    },

    /**
     * @description: 响应错误处理
     */
    responseInterceptorsCatch: (error: any) => {
        const { t } = useI18n()
        const errorLogStore = useErrorLogStoreWithOut()
        errorLogStore.addAjaxErrorInfo(error)
        const { response, code, msg, config } = error || {}
        const errorMessageMode = config?.requestOptions?.errorMessageMode || 'none'
        //scott 20211022 token失效提示信息
        //const msg: string = response?.data?.error?.msg ?? '';
        const msgVal: string = response?.data?.msg ?? ''
        const err: string = error?.toString?.() ?? ''
        let errMessage = ''
        try {
            if (code === 'ECONNABORTED' && msg.indexOf('timeout') !== -1) {
                errMessage = t('sys.api.apiTimeoutMessage')
            }
            if (err?.includes('Network Error')) {
                errMessage = t('sys.api.networkExceptionMsg')
            }

            if (errMessage) {
                if (errorMessageMode === 'modal') {
                    createErrorModal({ title: t('sys.api.errorTip'), content: errMessage })
                } else if (errorMessageMode === 'message') {
                    createMessage.error(errMessage)
                }
                return Promise.reject(error)
            }
        } catch (error) {
            // @ts-ignore
            throw new Error(error)
        }
        checkStatus(error?.response?.status, msgVal, errorMessageMode)
        return Promise.reject(error)
    }
}

function createAxios(opt?: Partial<CreateAxiosOptions>) {
    return new VAxios(
        deepMerge(
            {
                // See https://developer.mozilla.org/en-US/docs/Web/HTTP/Authentication#authentication_schemes
                // authentication schemes，e.g: Bearer
                // authenticationScheme: 'Bearer',
                authenticationScheme: '',
                //接口超时设置
                timeout: 60 * 1000,
                // 基础接口地址
                // baseURL: globSetting.apiUrl,
                // baseURL: '/manage/base/manage/iop',
                headers: { 'Content-Type': ContentTypeEnum.JSON },
                // 如果是form-data格式
                // headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
                // 数据处理方式
                transform,
                // 配置项，下面的选项都可以在独立的接口请求中覆盖
                requestOptions: {
                    // 默认将prefix 添加到url
                    joinPrefix: true,
                    // 是否返回原生响应头 比如：需要获取响应头时使用该属性
                    isReturnNativeResponse: false,
                    // 需要对返回数据进行处理
                    isTransformResponse: true,
                    // post请求的时候添加参数到url
                    joinParamsToUrl: false,
                    // 格式化提交参数时间
                    formatDate: true,
                    // 异常消息提示类型
                    errorMessageMode: 'message',
                    // 成功消息提示类型
                    successMessageMode: 'success',
                    // 接口地址
                    // apiUrl: globSetting.apiUrl,
                    // 接口拼接地址
                    urlPrefix: urlPrefix,
                    //  是否加入时间戳
                    joinTime: true,
                    // 忽略重复请求
                    ignoreCancelToken: true,
                    // 是否携带token
                    withToken: true
                }
            },
            opt || {}
        )
    )
}
export const defHttp = createAxios()

// other api url
// export const otherHttp = createAxios({
//   requestOptions: {
//     apiUrl: 'xxx',
//   },
// });
