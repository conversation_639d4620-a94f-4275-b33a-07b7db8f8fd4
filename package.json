{"name": "gac_iop_childapp", "version": "1.0.0", "author": {"name": "广汽国际统括平台", "email": "<EMAIL>"}, "scripts": {"pinstall": "pnpm install", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite", "dev": "vite --open", "build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 vite build && esno ./build/script/postBuild.ts", "build:bra": "cross-env NODE_ENV=bra NODE_OPTIONS=--max-old-space-size=8192 vite build --mode bra && esno ./build/script/postBuild.ts", "build:me": "cross-env NODE_ENV=me NODE_OPTIONS=--max-old-space-size=8192 vite build --mode me && esno ./build/script/postBuild.ts", "build:eu": "cross-env NODE_ENV=eu NODE_OPTIONS=--max-old-space-size=8192 vite build --mode eu && esno ./build/script/postBuild.ts", "build:rus": "cross-env NODE_ENV=rus NODE_OPTIONS=--max-old-space-size=8192 vite build --mode rus && esno ./build/script/postBuild.ts", "build:sea": "cross-env NODE_ENV=sea NODE_OPTIONS=--max-old-space-size=8192 vite build --mode sea && esno ./build/script/postBuild.ts", "build:bra_uat": "cross-env NODE_ENV=bra NODE_OPTIONS=--max-old-space-size=8192 vite build --mode bra_uat && esno ./build/script/postBuild.ts", "build:me_uat": "cross-env NODE_ENV=me NODE_OPTIONS=--max-old-space-size=8192 vite build --mode me_uat && esno ./build/script/postBuild.ts", "build:eu_uat": "cross-env NODE_ENV=eu NODE_OPTIONS=--max-old-space-size=8192 vite build --mode eu_uat && esno ./build/script/postBuild.ts", "build:rus_uat": "cross-env NODE_ENV=rus NODE_OPTIONS=--max-old-space-size=8192 vite build --mode rus_uat && esno ./build/script/postBuild.ts", "build:sea_uat": "cross-env NODE_ENV=sea NODE_OPTIONS=--max-old-space-size=8192 vite build --mode sea_uat && esno ./build/script/postBuild.ts", "build:test": "cross-env NODE_ENV=sandbox NODE_OPTIONS=--max-old-space-size=8192 vite build --mode sandbox && esno ./build/script/postBuild.ts", "build:uat": "cross-env NODE_ENV=uat NODE_OPTIONS=--max-old-space-size=8192 vite build --mode uat && esno ./build/script/postBuild.ts", "build:report": "pnpm clean:cache && cross-env REPORT=true npm run build", "preview": "npm run build && vite preview", "reinstall": "rimraf pnpm-lock.yaml && rimraf yarn.lock && rimraf package.lock.json && rimraf node_modules && npm run install", "clean:lib": "rimraf node_modules", "gen:icon": "esno ./build/generate/icon/index.ts", "batch:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "upgrade:log": "conventional-changelog -p angular -i CHANGELOG.md -s", "husky:install": "husky install"}, "dependencies": {"@ant-design/colors": "^7.2.0", "@ant-design/icons-vue": "^7.0.1", "@vant/area-data": "^1.5.2", "@vue/shared": "^3.5.13", "@vueuse/core": "^10.11.1", "@zxcvbn-ts/core": "^3.0.4", "ant-design-vue": "^4.2.6", "axios": "^1.7.9", "clipboard": "^2.0.11", "codemirror": "^5.65.18", "cron-parser": "^4.9.0", "cropperjs": "^1.6.2", "dayjs": "^1.11.13", "dom-align": "^1.12.4", "echarts": "^5.6.0", "emoji-mart-vue-fast": "^15.0.3", "event-source-polyfill": "^1.0.31", "lodash-es": "^4.17.21", "md5": "^2.3.0", "mockjs": "^1.1.0", "moment": "^2.30.1", "nprogress": "^0.2.0", "path-to-regexp": "^6.3.0", "pinia": "2.1.7", "print-js": "^1.6.0", "qrcode": "^1.5.4", "qs": "^6.13.1", "resize-observer-polyfill": "^1.5.1", "showdown": "^2.1.0", "sortablejs": "^1.15.6", "vant": "^4.9.19", "vditor": "^3.10.8", "vue": "^3.5.13", "vue-i18n": "^9.14.2", "vue-print-nb-jeecg": "^1.0.12", "vue-router": "^4.5.0", "vue-types": "^5.1.3", "vxe-table": "4.6.17", "vxe-table-plugin-antd": "4.0.7", "xe-utils": "3.5.26"}, "devDependencies": {"@purge-icons/generated": "^0.10.0", "@rys-fe/vite-plugin-theme": "^0.8.6", "@types/codemirror": "^5.60.15", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/jest": "^29.5.14", "@types/lodash-es": "^4.17.12", "@types/mockjs": "^1.0.10", "@types/node": "^20.17.12", "@types/nprogress": "^0.2.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.17", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "big.js": "^6.2.2", "conventional-changelog-cli": "^4.1.0", "cross-env": "^7.0.3", "dotenv": "^16.4.7", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.32.0", "esno": "^4.8.0", "fs-extra": "^11.2.0", "husky": "^8.0.3", "inquirer": "^9.3.7", "jest": "^29.7.0", "less": "^4.2.1", "picocolors": "^1.1.1", "prettier": "^3.4.2", "rollup": "^4.30.0", "typescript": "^4.9.5", "unocss": "^0.58.9", "vite": "^6.0.7", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.2", "vite-plugin-mkcert": "^1.17.6", "vite-plugin-mock": "^2.9.8", "vite-plugin-qiankun": "^1.0.15", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend-plus": "^0.1.0", "vue-eslint-parser": "^9.4.3"}, "repository": {"type": "git", "url": "git+https://github.com/jeecgboot/JeecgBoot.git"}, "license": "MIT", "bugs": {"url": "https://github.com/jeecgboot/JeecgBoot/issues"}, "homepage": "https://www.jeecg.com", "engines": {"node": "^18 || >=20"}}